#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据集整理脚本
将两个文件夹中的图片和标注数据重新组织到统一的结构中
"""

import os
import shutil
from pathlib import Path
import re

def create_output_directories():
    """创建输出目录结构"""
    output_dir = Path("organized_dataset")
    image_dir = output_dir / "images"
    answer_dir = output_dir / "answers"
    
    # 创建目录
    image_dir.mkdir(parents=True, exist_ok=True)
    answer_dir.mkdir(parents=True, exist_ok=True)
    
    return output_dir, image_dir, answer_dir

def process_11_30_folder(source_dir, image_dir, answer_dir):
    """
    处理11-30文件夹
    图片和标注在一起，提取prediction文件作为答案
    """
    source_path = Path(source_dir) / "202505（11-30）（原始+标注）" / "202505（11-30）（已标注）"
    
    if not source_path.exists():
        print(f"警告: 源目录不存在 {source_path}")
        return
    
    print(f"处理11-30文件夹: {source_path}")
    
    # 获取所有文件
    files = list(source_path.glob("*"))
    
    # 分类处理文件
    for file_path in files:
        filename = file_path.name
        
        # 处理图片文件
        if filename.endswith(('.png', '.jpg', '.jpeg')):
            # 复制图片到images目录
            dest_path = image_dir / filename
            shutil.copy2(file_path, dest_path)
            print(f"复制图片: {filename}")
        
        # 处理prediction文件（作为答案）
        elif filename.endswith('-prediction.txt'):
            # 复制prediction文件到answers目录，重命名为answer
            new_filename = filename.replace('-prediction.txt', '-answer.txt')
            dest_path = answer_dir / new_filename
            shutil.copy2(file_path, dest_path)
            print(f"复制答案: {filename} -> {new_filename}")
        
        # 忽略reasoning文件和其他txt文件
        elif filename.endswith('-reasoning.txt'):
            print(f"忽略reasoning文件: {filename}")
        elif filename.endswith('.txt') and not filename.endswith('-prediction.txt'):
            # 处理其他txt文件（如ta.txt等）
            dest_path = answer_dir / filename
            shutil.copy2(file_path, dest_path)
            print(f"复制其他文本文件: {filename}")

def process_86_127_folder(source_dir, image_dir, answer_dir):
    """
    处理86-127文件夹
    图片在原始文件夹，答案在已标注文件夹的GroundTruth文件中
    """
    # 原始图片目录
    original_dir = Path(source_dir) / "202505（86-127）" / "202505（61-127）原始"
    # 标注答案目录
    annotated_dir = Path(source_dir) / "202505（86-127）" / "已标注86-127"
    
    if not original_dir.exists():
        print(f"警告: 原始图片目录不存在 {original_dir}")
        return
    
    if not annotated_dir.exists():
        print(f"警告: 标注目录不存在 {annotated_dir}")
        return
    
    print(f"处理86-127文件夹图片: {original_dir}")
    print(f"处理86-127文件夹答案: {annotated_dir}")
    
    # 处理原始图片
    image_files = list(original_dir.glob("*"))
    for file_path in image_files:
        filename = file_path.name
        
        # 处理图片文件
        if filename.endswith(('.png', '.jpg', '.jpeg')):
            dest_path = image_dir / filename
            shutil.copy2(file_path, dest_path)
            print(f"复制图片: {filename}")
        
        # 处理其他txt文件（如ta.txt等）
        elif filename.endswith('.txt') and not any(x in filename for x in ['reasoning', 'prediction', 'GroundTruth']):
            dest_path = answer_dir / filename
            shutil.copy2(file_path, dest_path)
            print(f"复制其他文本文件: {filename}")
    
    # 处理GroundTruth答案文件
    answer_files = list(annotated_dir.glob("*GroundTruth.txt"))
    for file_path in answer_files:
        filename = file_path.name
        # 重命名GroundTruth文件为answer文件
        new_filename = filename.replace('-GroundTruth.txt', '-answer.txt')
        dest_path = answer_dir / new_filename
        shutil.copy2(file_path, dest_path)
        print(f"复制答案: {filename} -> {new_filename}")

def main():
    """主函数"""
    print("开始整理数据集...")
    
    # 创建输出目录
    output_dir, image_dir, answer_dir = create_output_directories()
    print(f"创建输出目录: {output_dir}")
    
    # 当前工作目录
    current_dir = Path(".")
    
    # 处理11-30文件夹
    print("\n=== 处理11-30文件夹 ===")
    process_11_30_folder(current_dir, image_dir, answer_dir)
    
    # 处理86-127文件夹
    print("\n=== 处理86-127文件夹 ===")
    process_86_127_folder(current_dir, image_dir, answer_dir)
    
    print(f"\n数据集整理完成！")
    print(f"输出目录: {output_dir.absolute()}")
    print(f"图片目录: {image_dir.absolute()}")
    print(f"答案目录: {answer_dir.absolute()}")
    
    # 统计文件数量
    image_count = len(list(image_dir.glob("*")))
    answer_count = len(list(answer_dir.glob("*")))
    print(f"\n统计信息:")
    print(f"图片文件数量: {image_count}")
    print(f"答案文件数量: {answer_count}")

if __name__ == "__main__":
    main()
